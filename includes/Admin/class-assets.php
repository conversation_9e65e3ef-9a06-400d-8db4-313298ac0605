<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

class Review_Suite_Admin_Assets {
	private $pages = array( 'toplevel_page_gutensuite/review-suite' );
	
	public function __construct() {
		add_action( 'admin_enqueue_scripts', array( $this, 'plugin_scripts' ) );
	}

	public function plugin_scripts( $hook ) {
		if ( ! in_array( $hook, $this->pages, true ) ) {
			return;
		}
		$dependencies = include_once REVIEW_SUITE_ASSETS_DIR_PATH . 'js/review-suite.core.min.asset.php';
		wp_enqueue_style( 'review-suite-admin', REVIEW_SUITE_ASSETS_URI . 'js/review-suite.core.min.css', array( 'wp-components' ), $dependencies['version'], 'all' );
		wp_enqueue_script(
			'review-suite',
			REVIEW_SUITE_ASSETS_URI . 'js/review-suite.core.min.js',
			array_merge( $dependencies['dependencies'] ),
			$dependencies['version'],
			true
		);

		$cache_data = $this->get_cache_data();

		wp_localize_script(
			'review-suite',
			'reviewSuite',
			array(
				'ajaxurl'         => admin_url( 'admin-ajax.php' ),
				'nonce'           => wp_create_nonce( 'review_suite_nonce' ),
				'plugin_root_url' => REVIEW_SUITE_PLUGIN_ROOT_URI,
				'data'            => $cache_data['data'] ?? array(),
				'count'           => $cache_data['count'] ?? 0,
				'last_updated'    => $cache_data['last_updated'] ?? null,
				'original_domain' => $cache_data['original_domain'] ?? '',
				'domain'          => $cache_data['domain'] ?? '',
			)
		);
	}

	public function get_cache_data() {
		$data_map = array(
			'original_domain' => 'review_suite_tp_reviews_original_domain',
			'domain'          => 'review_suite_tp_reviews_domain',
			'data'            => 'review_suite_tp_reviews_data',
			'misc_data'       => 'review_suite_tp_reviews_misc_data',
			'business_data'   => 'review_suite_tp_reviews_business_data',
			'count'           => 'review_suite_tp_reviews_count',
			'last_updated'    => 'review_suite_tp_reviews_last_updated',
		);

		$results = array();
		foreach ( $data_map as $key => $option_name ) {
			$option_value = get_option( $option_name, null );
			if ( $option_value !== null ) {
				$results[] = array(
					'option_name'  => $option_name,
					'option_value' => $option_value,
				);
			}
		}

		$data = array();

		foreach ( $results as $result ) {
			foreach ( $data_map as $key => $prefix ) {
				if ( substr( $result['option_name'], 0, strlen( $prefix ) ) === $prefix ) {
					if ( $key === 'data' || $key === 'misc_data' || $key === 'business_data' ) {
						$unserialize_data = maybe_unserialize( $result['option_value'] );
						$data[ $key ]     = $unserialize_data;
					} else {
						$data[ $key ] = $result['option_value'];
					}
				}
			}
		}

		// Reconstruct the complete API response for frontend compatibility
		if ( isset( $data['misc_data'] ) && isset( $data['business_data'] ) && isset( $data['data'] ) ) {
			$complete_data                     = $data['misc_data'];
			$complete_data['business_details'] = $data['business_data'];
			$complete_data['reviews']          = $data['data'];
			$data['data']                      = $complete_data;
		}

		return $data;
	}
}
