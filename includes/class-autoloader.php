<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * SPL Autoloader for Review Suite plugin
 *
 * This autoloader follows WordPress coding standards and loads classes
 * with the Review_Suite_ prefix from the includes directory.
 */
class Review_Suite_Autoloader {

	/**
	 * The base directory for class files
	 *
	 * @var string
	 */
	private $base_dir;

	/**
	 * Constructor
	 *
	 * @param string $base_dir The base directory for class files
	 */
	public function __construct( $base_dir ) {
		$this->base_dir = rtrim( $base_dir, '/' ) . '/';
	}

	/**
	 * Register the autoloader
	 */
	public function register() {
		spl_autoload_register( array( $this, 'load_class' ) );
	}

	/**
	 * Load a class file
	 *
	 * @param string $class_name The class name to load
	 * @return bool True if the class was loaded, false otherwise
	 */
	public function load_class( $class_name ) {
		// Only handle classes with our prefix
		if ( strpos( $class_name, 'Review_Suite_' ) !== 0 ) {
			return false;
		}

		// Remove the prefix
		$class_name = substr( $class_name, 13 ); // Remove 'Review_Suite_'

		// Convert class name to file path
		$file_path = $this->class_name_to_file_path( $class_name );

		// Build the full file path
		$full_path = $this->base_dir . $file_path;

		// Load the file if it exists
		if ( file_exists( $full_path ) ) {
			require_once $full_path;
			return true;
		}

		return false;
	}

	/**
	 * Convert class name to file path
	 *
	 * @param string $class_name The class name
	 * @return string The file path
	 */
	private function class_name_to_file_path( $class_name ) {
		// Handle Admin classes
		if ( strpos( $class_name, 'Admin_' ) === 0 ) {
			$sub_class = substr( $class_name, 6 ); // Remove 'Admin_'
			return 'Admin/class-' . strtolower( str_replace( '_', '-', $sub_class ) ) . '.php';
		}

		// Handle Shortcode classes
		if ( strpos( $class_name, 'Shortcodes_' ) === 0 ) {
			$sub_class = substr( $class_name, 11 ); // Remove 'Shortcodes_'
			return 'Shortcodes/class-' . strtolower( str_replace( '_', '-', $sub_class ) ) . '.php';
		}

		// Handle main classes
		return 'class-' . strtolower( str_replace( '_', '-', $class_name ) ) . '.php';
	}
}
