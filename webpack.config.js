const defaultConfig = require("@wordpress/scripts/config/webpack.config");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const path = require("path");
const { exit } = require("process");

const config = {
	...defaultConfig,
	entry: {
		"review-suite.core.min": path.resolve(__dirname, "react_app/index.js"),
	},
	output: {
		path: path.resolve(__dirname, "assets/js"),
		filename: "[name].js",
		// CSS will be output by MiniCssExtractPlugin, see below
	},
	plugins: [...defaultConfig.plugins, new CleanWebpackPlugin()],
	externals: {
		jquery: "jQuery",
		underscore: "_",
		lodash: "lodash",
		react: ["vendor", "React"],
		"react-dom": ["vendor", "ReactDOM"],
		// WordPress dependencies.
		"@wordpress/i18n": ["vendor", "wp", "i18n"],
		"@wordpress/hooks": ["vendor", "wp", "hooks"],
		"@wordpress/components": ["vendor", "wp", "components"],
	},
};

module.exports = config;
