import { __ } from "@wordpress/i18n";

export const {
	ajaxurl,
	nonce,
	plugin_root_url,
	data,
	count,
	last_updated,
	domain,
	original_domain,
} = window.reviewSuite;
/**
 * Utility functions for the Review Suite
 */

/**
 * Fetches business reviews for a specified domain using direct API call
 *
 * @param {string} domain - The domain to fetch reviews for (e.g., "divinext.com")
 * @param {Object} options - Optional configuration options
 * @param {number} options.timeout - Request timeout in milliseconds (default: 10000)
 * @param {AbortSignal} options.signal - AbortController signal for cancelling the request
 * @returns {Promise<Object>} - Promise that resolves to the reviews data
 * @throws {Error} - Throws an error if the request fails
 */
export const getReviewsFromAPI = async (domain, options = {}) => {
	if (!domain) {
		throw new Error("Domain is required");
	}

	// Remove any protocol and trailing slashes to ensure consistent format
	const cleanDomain = domain
		.replace(/^(https?:\/\/)?(www\.)?/, "")
		.replace(/\/$/, "");

	const { timeout = 10000, signal } = options;

	try {
		// Create a controller for timeout if not provided externally
		const controller = signal ? null : new AbortController();
		const timeoutId = signal
			? null
			: setTimeout(() => controller.abort(), timeout);

		const response = await fetch(
			`https://api.gutensuite.net/data/tp/v1/reviews/${encodeURIComponent(
				cleanDomain
			)}`,
			{
				method: "GET",
				headers: {
					Accept: "application/json",
					"Content-Type": "application/json",
				},
				signal: signal || controller.signal,
			}
		);

		// Clear timeout if we created one
		if (timeoutId) {
			clearTimeout(timeoutId);
		}

		if (!response.ok) {
			throw new Error(
				`Failed to fetch reviews: ${response.status} ${response.statusText}`
			);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		if (error.name === "AbortError") {
			throw new Error("Request timed out");
		}
		throw error;
	}
};

/**
 * Fetches business reviews for a specified domain using WordPress AJAX
 *
 * @param {string} domain - The domain to fetch reviews for (e.g., "divinext.com")
 * @param {Object} options - Optional configuration options
 * @param {number} options.timeout - Request timeout in milliseconds (default: 30000)
 * @returns {Promise<Object>} - Promise that resolves to the reviews data
 * @throws {Error} - Throws an error if the request fails
 */
export const getReviews = (domain, options = {}) => {
	if (!domain) {
		return Promise.reject(new Error("Domain is required"));
	}

	const { timeout = 30000 } = options;

	// Ensure we have access to WordPress ajax functionality
	if (
		typeof window.ajaxurl === "undefined" ||
		typeof window.reviewSuite === "undefined"
	) {
		return Promise.reject(
			new Error(
				"WordPress AJAX not available. Make sure the script is properly enqueued."
			)
		);
	}

	return new Promise((resolve, reject) => {
		// Create a FormData object for the request
		const formData = new FormData();
		formData.append("action", "review_suite_get_reviews");
		formData.append("security", nonce);
		formData.append("domain", domain);
		formData.append("revalidate", options.revalidate);

		// Use jQuery if available, otherwise use fetch
		if (window.jQuery) {
			jQuery.ajax({
				url: window.ajaxurl,
				type: "POST",
				data: formData,
				processData: false,
				contentType: false,
				timeout: timeout,
				success: function (response) {
					if (response.success) {
						resolve(response.data);
					} else {
						reject(
							new Error(response.data?.message || "Unknown error occurred")
						);
					}
				},
				error: function (xhr, status, error) {
					if (status === "timeout") {
						reject(new Error("Request timed out"));
					} else {
						reject(new Error(`AJAX request failed: ${error}`));
					}
				},
			});
		} else {
			// Fallback to using fetch API if jQuery is not available
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), timeout);

			fetch(window.ajaxurl, {
				method: "POST",
				body: formData,
				signal: controller.signal,
			})
				.then((response) => {
					clearTimeout(timeoutId);
					if (!response.ok) {
						throw new Error(
							`Server returned ${response.status}: ${response.statusText}`
						);
					}
					return response.json();
				})
				.then((response) => {
					if (response.success) {
						resolve(response.data);
					} else {
						reject(
							new Error(response.data?.message || "Unknown error occurred")
						);
					}
				})
				.catch((error) => {
					clearTimeout(timeoutId);
					if (error.name === "AbortError") {
						reject(new Error("Request timed out"));
					} else {
						reject(error);
					}
				});
		}
	});
};
