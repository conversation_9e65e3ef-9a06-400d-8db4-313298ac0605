import { useState } from "react";
import { __ } from "@wordpress/i18n";
import { TextControl, Button, Spinner } from "@wordpress/components";
import { getReviews, original_domain } from "../../helper/utils";
import { useDataContext } from "../../context/data-context";

const Reviews = () => {
	const { data = {}, setData } = useDataContext();
	const { business_details = {}, reviews = [] } = data;
	const [businessUrl, setBusinessUrl] = useState(original_domain);
	const [loading, setLoading] = useState(false);

	const __fetchReviews = async (revalidate = false) => {
		setLoading(true);
		const response = await getReviews(businessUrl, { revalidate: revalidate });
		if (response?.data) {
			setData(response.data);
		}
		setLoading(false);
	};

	return (
		<div className="page review-page">
			<div className="review-fetch">
				<TextControl
					__nextHasNoMarginBottom
					__next40pxDefaultSize
					label={__("Business URL", "review-suite")}
					type="url"
					help={__("Enter the URL of your business page", "review-suite")}
					value={businessUrl}
					onChange={(value) => setBusinessUrl(value)}
					placeholder="Your Business Page URL"
				/>
				<div className="button-container">
					<Button
						variant="primary"
						style={{ backgroundColor: "#28a745", fontWeight: "600" }}
						onClick={__fetchReviews}
						disabled={businessUrl === ""}
					>
						{__("Fetch Reviews", "review-suite")}
					</Button>
					<Button
						variant="secondary"
						onClick={() => __fetchReviews(true)}
						disabled={!data || businessUrl === ""}
					>
						{__("Sync Reviews", "review-suite")}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default Reviews;
