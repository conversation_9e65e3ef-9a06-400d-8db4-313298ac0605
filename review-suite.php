<?php
/**
 * Plugin Name:       Review Suite
 * Description:       Automatically sync and display your business reviews on your WordPress site with customizable widgets, trust badges, and real-time updates. Boost credibility and conversions by showcasing genuine customer testimonials anywhere on your site with easy shortcodes and flexible layouts.
 * Version:           1.0.0
 * Requires at least: 6.8
 * Requires PHP:      7.4
 * Author:            GutenSuite
 * License:           GPL-2.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       review-suite
 *
 * @package Review_Suite
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! file_exists( __DIR__ . '/includes/class-autoloader.php' ) ) {
	exit;
}
require_once __DIR__ . '/includes/class-autoloader.php';

// Initialize the autoloader
$autoloader = new Review_Suite_Autoloader( __DIR__ . '/includes' );
$autoloader->register();

if ( ! class_exists( 'Review_Suite' ) ) {
	final class Review_Suite {

		private function __construct() {
			$this->define_constants();
			register_activation_hook( __FILE__, array( $this, 'activate' ) );
			register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
			add_action( 'plugins_loaded', array( $this, 'on_plugins_loaded' ) );
			add_action( 'review_suite_loaded', array( $this, 'init_plugin' ) );
		}

		public static function init() {
			static $instance = false;

			if ( ! $instance ) {
				$instance = new self();
			}

			return $instance;
		}

		/**
		 * Defines CONSTANTS for Whole plugins.
		 */
		public function define_constants() {
			define( 'REVIEW_SUITE_SLUG', 'review-suite' );
			define( 'REVIEW_SUITE_PLUGIN_ROOT_URI', plugins_url( '/', __FILE__ ) );
			define( 'REVIEW_SUITE_ROOT_DIR_PATH', plugin_dir_path( __FILE__ ) );
			define( 'REVIEW_SUITE_ASSETS_DIR_PATH', REVIEW_SUITE_ROOT_DIR_PATH . 'assets/' );
			define( 'REVIEW_SUITE_ASSETS_URI', REVIEW_SUITE_PLUGIN_ROOT_URI . 'assets/' );
			define( 'REVIEW_SUITE_TP_API', 'https://api.gutensuite.net/data/tp/v1/reviews/' );
		}


		public function on_plugins_loaded() {
			do_action( 'review_suite_loaded' );
		}

		/**
		 * Initialize the plugin
		 *
		 * @return void
		 */
		public function init_plugin() {
			if ( is_admin() ) {
				new Review_Suite_Admin();
			}
			new Review_Suite_Admin_Assets();

			Review_Suite_Business_Data::init_default_data();
		}

		public function activate() {}

		public function deactivate() {}
	}
}

/**
 * Initializes the main plugin
 *
 * @return \Review_Suite
 */
if ( ! function_exists( 'Review_Suite_Start' ) ) {
	function Review_Suite_Start() {
		return Review_Suite::init();
	}
}

// Plugin Start
Review_Suite_Start();
