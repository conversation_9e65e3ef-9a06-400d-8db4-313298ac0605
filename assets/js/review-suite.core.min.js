(()=>{"use strict";const e=window.ReactDOM,t=window.React,r=window.wp.i18n,{ajaxurl:a,nonce:n,plugin_root_url:s,data:o,count:i,last_updated:d,domain:u,original_domain:c}=window.reviewSuite,l=window.ReactJSXRuntime,w=(0,t.createContext)();function v({children:e}){const[r,a]=(0,t.useState)("dashboard"),[n,s]=(0,t.useState)(o||{}),i={tab:r,setTab:a,data:n,setData:s};return(0,l.jsx)(w.Provider,{value:i,children:e})}function m(){return(0,t.useContext)(w)}const h=()=>{const{tab:e,setTab:t}=m();return(0,l.jsx)("nav",{children:(0,l.jsx)("ul",{children:b.map(r=>(0,l.jsx)("li",{className:e===r.tab?"active":"",onClick:()=>t(r.tab),children:r.label},r.label))})})},b=[{label:"Dashboard",tab:"dashboard"}],p=window.wp.components,x=()=>{const{data:e={},setData:a}=m(),{business_details:s={},reviews:o=[]}=e,[i,d]=(0,t.useState)(c),[u,w]=(0,t.useState)(!1),v=async(e=!1)=>{w(!0);const t=await((e,t={})=>{if(!e)return Promise.reject(new Error("Domain is required"));const{timeout:r=3e4}=t;return void 0===window.ajaxurl||void 0===window.reviewSuite?Promise.reject(new Error("WordPress AJAX not available. Make sure the script is properly enqueued.")):new Promise((a,s)=>{const o=new FormData;if(o.append("action","review_suite_get_reviews"),o.append("security",n),o.append("domain",e),o.append("revalidate",t.revalidate),window.jQuery)jQuery.ajax({url:window.ajaxurl,type:"POST",data:o,processData:!1,contentType:!1,timeout:r,success:function(e){e.success?a(e.data):s(new Error(e.data?.message||"Unknown error occurred"))},error:function(e,t,r){s("timeout"===t?new Error("Request timed out"):new Error(`AJAX request failed: ${r}`))}});else{const e=new AbortController,t=setTimeout(()=>e.abort(),r);fetch(window.ajaxurl,{method:"POST",body:o,signal:e.signal}).then(e=>{if(clearTimeout(t),!e.ok)throw new Error(`Server returned ${e.status}: ${e.statusText}`);return e.json()}).then(e=>{e.success?a(e.data):s(new Error(e.data?.message||"Unknown error occurred"))}).catch(e=>{clearTimeout(t),"AbortError"===e.name?s(new Error("Request timed out")):s(e)})}})})(i,{revalidate:e});t?.data&&a(t.data),w(!1)};return(0,l.jsx)("div",{className:"page review-page",children:(0,l.jsxs)("div",{className:"review-fetch",children:[(0,l.jsx)(p.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,r.__)("Business URL","review-suite"),type:"url",help:(0,r.__)("Enter the URL of your business page","review-suite"),value:i,onChange:e=>d(e),placeholder:"Your Business Page URL"}),(0,l.jsxs)("div",{className:"button-container",children:[(0,l.jsx)(p.Button,{variant:"primary",style:{backgroundColor:"#28a745",fontWeight:"600"},onClick:v,disabled:""===i,children:(0,r.__)("Fetch Reviews","review-suite")}),(0,l.jsx)(p.Button,{variant:"secondary",onClick:()=>v(!0),disabled:!e||""===i,children:(0,r.__)("Sync Reviews","review-suite")})]})]})})},j=()=>{const{tab:e}=m();return(0,l.jsxs)("div",{children:[(0,l.jsx)(h,{}),"dashboard"===e&&(0,l.jsx)(x,{})]})};document.addEventListener("DOMContentLoaded",function(){const t=document.getElementById("review-suite-body");(0,e.createRoot)(t).render((0,l.jsx)(v,{children:(0,l.jsx)(j,{})}))})})();